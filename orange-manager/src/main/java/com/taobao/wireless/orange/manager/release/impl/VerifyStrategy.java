package com.taobao.wireless.orange.manager.release.impl;

import com.taobao.wireless.orange.common.constant.enums.OperationType;
import com.taobao.wireless.orange.common.constant.enums.ReleaseOrderStatus;
import com.taobao.wireless.orange.common.exception.CommonException;
import com.taobao.wireless.orange.common.exception.ExceptionEnum;
import com.taobao.wireless.orange.common.thread.ThreadContextUtil;
import com.taobao.wireless.orange.manager.release.AbstractReleaseOperationTemplate;
import com.taobao.wireless.orange.manager.release.ReleaseOperationContext;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * 验证操作策略实现
 */
@Component
public class VerifyOperationStrategy extends AbstractReleaseOperationTemplate {

    @Override
    public OperationType getOperationType() {
        return OperationType.VERIFY;
    }

    @Override
    public void validateStatus(ReleaseOperationContext context) {
        var releaseOrder = context.getReleaseOrder();

        if (!ReleaseOrderStatus.WAIT_VERIFY.equals(releaseOrder.getStatus())) {
            throw new CommonException(ExceptionEnum.RELEASE_ORDER_STATUS_INVALID);
        }
    }

    @Override
    public void validatePermission(ReleaseOperationContext context) {
        var namespace = context.getNamespace();
        String workerId = ThreadContextUtil.getWorkerId();

        if (StringUtils.isBlank(workerId) || !namespace.getTesters().contains(workerId)) {
            throw new CommonException(ExceptionEnum.NO_PERMISSION);
        }
    }

    @Override
    public void validateParameters(ReleaseOperationContext context) {
    }

    @Override
    public void executeOperation(ReleaseOperationContext context) {
    }

    @Override
    public ReleaseOrderStatus getTargetStatus() {
        return ReleaseOrderStatus.VERIFY_PASS;
    }
}